-- Script pour ajouter la colonne image_url à la table trains
-- Date: 2025-05-28
-- Description: Ajoute un champ pour stocker l'URL de l'image du train

USE train_tickets;

-- Ajouter la colonne image_url à la table trains
ALTER TABLE trains 
ADD COLUMN image_url VARCHAR(500) NULL 
COMMENT 'URL de l\'image du train';

-- Mettre à jour quelques trains existants avec des images par défaut
UPDATE trains SET image_url = '/train-tickets/images/trains/tgv-default.jpg' WHERE train_type = 'TGV';
UPDATE trains SET image_url = '/train-tickets/images/trains/intercites-default.jpg' WHERE train_type = 'INTERCITES';
UPDATE trains SET image_url = '/train-tickets/images/trains/ter-default.jpg' WHERE train_type = 'TER';
UPDATE trains SET image_url = '/train-tickets/images/trains/ouigo-default.jpg' WHERE train_type = 'OUIGO';
UPDATE trains SET image_url = '/train-tickets/images/trains/thalys-default.jpg' WHERE train_type = 'THALYS';
UPDATE trains SET image_url = '/train-tickets/images/trains/eurostar-default.jpg' WHERE train_type = 'EUROSTAR';

-- Vérifier les modifications
SELECT id, train_number, name, train_type, image_url FROM trains LIMIT 10;

COMMIT;
