package com.trainticket.model;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.util.ArrayList;
import java.util.List;

@Entity
@Table(name = "trains")
public class Train {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank(message = "Le numéro de train est obligatoire")
    @Column(name = "train_number", unique = true, nullable = false)
    private String trainNumber;
    
    @NotBlank(message = "Le nom du train est obligatoire")
    @Column(nullable = false)
    private String name;
    
    @NotNull(message = "La capacité est obligatoire")
    @Positive(message = "La capacité doit être positive")
    @Column(nullable = false)
    private Integer capacity;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "train_type", nullable = false)
    private TrainType trainType;
    
    @Column(name = "is_active")
    private Boolean isActive = true;

    @Column(name = "image_url")
    private String imageUrl;

    @OneToMany(mappedBy = "train", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    private List<Route> routes = new ArrayList<>();
    
    // Constructeurs
    public Train() {}
    
    public Train(String trainNumber, String name, Integer capacity, TrainType trainType) {
        this.trainNumber = trainNumber;
        this.name = name;
        this.capacity = capacity;
        this.trainType = trainType;
    }
    
    // Getters et Setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getTrainNumber() {
        return trainNumber;
    }
    
    public void setTrainNumber(String trainNumber) {
        this.trainNumber = trainNumber;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public Integer getCapacity() {
        return capacity;
    }
    
    public void setCapacity(Integer capacity) {
        this.capacity = capacity;
    }
    
    public TrainType getTrainType() {
        return trainType;
    }
    
    public void setTrainType(TrainType trainType) {
        this.trainType = trainType;
    }
    
    public Boolean getIsActive() {
        return isActive;
    }
    
    public void setIsActive(Boolean isActive) {
        this.isActive = isActive;
    }

    public String getImageUrl() {
        return imageUrl;
    }

    public void setImageUrl(String imageUrl) {
        this.imageUrl = imageUrl;
    }

    public List<Route> getRoutes() {
        return routes;
    }
    
    public void setRoutes(List<Route> routes) {
        this.routes = routes;
    }
    
    @Override
    public String toString() {
        return "Train{" +
                "id=" + id +
                ", trainNumber='" + trainNumber + '\'' +
                ", name='" + name + '\'' +
                ", capacity=" + capacity +
                ", trainType=" + trainType +
                ", isActive=" + isActive +
                ", imageUrl='" + imageUrl + '\'' +
                '}';
    }
}
