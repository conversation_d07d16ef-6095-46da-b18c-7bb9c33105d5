package com.trainticket.service;

import com.trainticket.dao.RouteDAO;
import com.trainticket.dao.impl.RouteDAOImpl;
import com.trainticket.model.Route;
import com.trainticket.model.Train;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

public class RouteService {

    private final RouteDAO routeDAO;

    public RouteService() {
        this.routeDAO = new RouteDAOImpl();
    }

    public RouteService(RouteDAO routeDAO) {
        this.routeDAO = routeDAO;
    }

    public RouteDAO getRouteDAO() {
        return routeDAO;
    }

    public List<Route> searchRoutes(String departureCity, String arrivalCity) {
        if (departureCity == null || arrivalCity == null ||
                departureCity.trim().isEmpty() || arrivalCity.trim().isEmpty()) {
            throw new IllegalArgumentException("Les villes de départ et d'arrivée sont obligatoires");
        }

        return routeDAO.findByDepartureAndArrivalCities(departureCity.trim(), arrivalCity.trim());
    }

    /**
     * Méthode simplifiée pour récupérer tous les trajets (pour test)
     */
    public List<Route> getAllRoutesSimple() {
        return routeDAO.findAll();
    }

    /**
     * Recherche simplifiée et dynamique - récupère directement de votre base de données
     */
    public List<Route> searchRoutesSimple(String departureCity, String arrivalCity) {
        System.out.println("🔍 Recherche dynamique dans la table 'routes'");
        System.out.println("   Départ: '" + departureCity + "'");
        System.out.println("   Arrivée: '" + arrivalCity + "'");

        // Utiliser la méthode DAO qui gère les paramètres vides
        List<Route> results = routeDAO.findByDepartureAndArrivalCities(departureCity, arrivalCity);

        System.out.println("📊 Résultats trouvés: " + results.size() + " trajets");

        return results;
    }

    /**
     * Recherche 100% dynamique avec requêtes SQL natives
     */
    public List<Route> searchRoutesDynamic(String departureCity, String arrivalCity,
                                          String date, String time) {
        System.out.println("🔍 Recherche dynamique avec SQL natif...");
        System.out.println("   Départ: " + departureCity);
        System.out.println("   Arrivée: " + arrivalCity);
        System.out.println("   Date: " + date);
        System.out.println("   Heure: " + time);

        return routeDAO.findByDynamicSearch(departureCity, arrivalCity, date, time);
    }

    /**
     * Récupère tous les trajets avec SQL natif
     */
    public List<Route> getAllRoutesNative() {
        System.out.println("📊 Récupération de tous les trajets avec SQL natif...");
        return routeDAO.findAllNative();
    }

    public List<Route> searchRoutes(String departureCity, String arrivalCity,
                                    LocalDateTime startDateTime, LocalDateTime endDateTime) {
        if (departureCity == null || arrivalCity == null || startDateTime == null || endDateTime == null ||
                departureCity.trim().isEmpty() || arrivalCity.trim().isEmpty()) {
            throw new IllegalArgumentException("Les villes de départ, d'arrivée et les dates sont obligatoires");
        }

        return routeDAO.findByDepartureAndArrivalCitiesAndDate(
                        departureCity.trim(), arrivalCity.trim(), startDateTime, endDateTime)
                .stream()
                .filter(route -> route.getDepartureTime().isAfter(LocalDateTime.now()))
                .collect(Collectors.toList());
    }

    public List<Route> searchRoutes(String departureCity, String arrivalCity, LocalDate date) {
        if (departureCity == null || arrivalCity == null || date == null ||
                departureCity.trim().isEmpty() || arrivalCity.trim().isEmpty()) {
            throw new IllegalArgumentException("Les villes de départ, d'arrivée et la date sont obligatoires");
        }

        LocalDateTime startOfDay = date.atStartOfDay();
        LocalDateTime endOfDay = date.atTime(LocalTime.MAX);

        return routeDAO.findByDepartureAndArrivalCitiesAndDate(
                        departureCity.trim(), arrivalCity.trim(), startOfDay, endOfDay);
    }

    public List<Route> searchAvailableRoutes(String departureCity, String arrivalCity,
                                             LocalDate date, int passengerCount) {
        List<Route> routes = searchRoutes(departureCity, arrivalCity, date);
        return routes.stream()
                .filter(route -> route.hasAvailableSeats(passengerCount))
                .collect(Collectors.toList());
    }

    // Ajouter une méthode pour filtrer par plage horaire
    public List<Route> filterByTimeRange(List<Route> routes, String timeRange) {
        if (timeRange == null || timeRange.trim().isEmpty()) {
            return routes;
        }

        String[] timeParts = timeRange.split("-");
        if (timeParts.length != 2) {
            return routes; // Plage horaire invalide, retourner tous les trajets
        }

        try {
            LocalTime startTime = LocalTime.parse(timeParts[0]);
            LocalTime endTime = LocalTime.parse(timeParts[1]);

            return routes.stream()
                    .filter(route -> {
                        if (route.getDepartureTime() == null) {
                            return false;
                        }
                        LocalTime departureTime = route.getDepartureTime().toLocalTime();
                        return !departureTime.isBefore(startTime) && !departureTime.isAfter(endTime);
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            return routes; // En cas d'erreur de parsing, retourner tous les trajets
        }
    }

    public Optional<Route> findById(Long id) {
        return routeDAO.findById(id);
    }

    public List<Route> findAll() {
        return routeDAO.findAll();
    }

    public List<Route> findActiveRoutes() {
        return routeDAO.findAll()
                .stream()
                .filter(route -> route.getIsActive() != null && route.getIsActive())
                .collect(Collectors.toList());
    }

    public List<Route> findFutureRoutes() {
        return routeDAO.findFutureRoutes();
    }

    public List<Route> findRoutesByTrain(Train train) {
        return routeDAO.findByTrain(train);
    }

    public List<Route> findRoutesByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        return routeDAO.findByDateRange(startDate, endDate);
    }

    public List<String> getAllDepartureCities() {
        return routeDAO.findDistinctDepartureCities();
    }

    public List<String> getAllArrivalCities() {
        return routeDAO.findDistinctArrivalCities();
    }

    public List<String> getArrivalCitiesFromDeparture(String departureCity) {
        return routeDAO.findByDepartureCity(departureCity)
                .stream()
                .map(Route::getArrivalCity)
                .distinct()
                .sorted()
                .collect(Collectors.toList());
    }

    public boolean isRouteAvailable(Long routeId, int passengerCount) {
        Optional<Route> routeOpt = routeDAO.findById(routeId);
        if (routeOpt.isEmpty()) {
            return false;
        }

        Route route = routeOpt.get();
        if (!route.getIsActive() || route.getDepartureTime().isBefore(LocalDateTime.now())) {
            return false;
        }

        return route.hasAvailableSeats(passengerCount);
    }

    public boolean reserveSeats(Long routeId, int passengerCount) {
        Optional<Route> routeOpt = routeDAO.findById(routeId);
        if (routeOpt.isEmpty()) {
            return false;
        }

        Route route = routeOpt.get();
        if (!isRouteAvailable(routeId, passengerCount)) {
            return false;
        }

        try {
            route.reserveSeats(passengerCount);
            routeDAO.update(route);
            return true;
        } catch (IllegalStateException e) {
            return false;
        }
    }

    public void releaseSeats(Long routeId, int passengerCount) {
        Optional<Route> routeOpt = routeDAO.findById(routeId);
        if (routeOpt.isPresent()) {
            Route route = routeOpt.get();
            route.releaseSeats(passengerCount);
            routeDAO.update(route);
        }
    }

    public List<Route> findPopularRoutes(int limit) {
        return routeDAO.findFutureRoutes()
                .stream()
                .filter(route -> route.getAvailableSeats() != null && route.getTrain() != null)
                .sorted((r1, r2) -> {
                    double occupancy1 = 1.0 - (double) r1.getAvailableSeats() / r1.getTrain().getCapacity();
                    double occupancy2 = 1.0 - (double) r2.getAvailableSeats() / r2.getTrain().getCapacity();
                    return Double.compare(occupancy2, occupancy1);
                })
                .limit(limit)
                .collect(Collectors.toList());
    }

    public double getOccupancyRate(Route route) {
        if (route.getTrain() == null || route.getAvailableSeats() == null) {
            return 0.0;
        }

        int totalCapacity = route.getTrain().getCapacity();
        int occupiedSeats = totalCapacity - route.getAvailableSeats();
        return (double) occupiedSeats / totalCapacity;
    }

    public long getTotalRoutesCount() {
        return routeDAO.count();
    }

    public long getActiveRoutesCount() {
        return routeDAO.countActiveRoutes();
    }

    public List<Route> getPopularRoutes(int limit) {
        return findPopularRoutes(limit);
    }

    public List<Route> findRoutesByDepartureCity(String departureCity) {
        if (departureCity == null || departureCity.trim().isEmpty()) {
            throw new IllegalArgumentException("La ville de départ est obligatoire");
        }

        return routeDAO.findByDepartureCity(departureCity.trim())
                .stream()
                .filter(route -> route.getDepartureTime().isAfter(LocalDateTime.now()))
                .collect(Collectors.toList());
    }

    public List<Route> findRoutesByArrivalCity(String arrivalCity) {
        if (arrivalCity == null || arrivalCity.trim().isEmpty()) {
            throw new IllegalArgumentException("La ville d'arrivée est obligatoire");
        }

        return routeDAO.findByArrivalCity(arrivalCity.trim())
                .stream()
                .filter(route -> route.getDepartureTime().isAfter(LocalDateTime.now()))
                .collect(Collectors.toList());
    }
}