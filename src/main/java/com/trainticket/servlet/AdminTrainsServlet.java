package com.trainticket.servlet;

import com.trainticket.model.Train;
import com.trainticket.model.TrainType;
import com.trainticket.model.User;
import com.trainticket.model.UserRole;
import com.trainticket.service.TrainService;
import com.trainticket.util.SessionUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.List;

@WebServlet(name = "AdminTrainsServlet", urlPatterns = {"/admin/trains", "/admin/trains/add", "/admin/trains/edit", "/admin/trains/delete"})
public class AdminTrainsServlet extends HttpServlet {

    private TrainService trainService;

    @Override
    public void init() throws ServletException {
        trainService = new TrainService();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Vérifier que l'utilisateur est connecté et est admin
        User currentUser = SessionUtil.getCurrentUser(request);
        if (currentUser == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }

        if (currentUser.getRole() != UserRole.ADMIN) {
            response.sendError(HttpServletResponse.SC_FORBIDDEN, "Accès refusé. Droits administrateur requis.");
            return;
        }

        String path = request.getServletPath();

        switch (path) {
            case "/admin/trains":
                showTrainsList(request, response, currentUser);
                break;
            case "/admin/trains/add":
                showAddTrainForm(request, response, currentUser);
                break;
            case "/admin/trains/edit":
                showEditTrainForm(request, response, currentUser);
                break;
            case "/admin/trains/delete":
                handleDeleteTrain(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Vérifier que l'utilisateur est connecté et est admin
        User currentUser = SessionUtil.getCurrentUser(request);
        if (currentUser == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }

        if (currentUser.getRole() != UserRole.ADMIN) {
            response.sendError(HttpServletResponse.SC_FORBIDDEN, "Accès refusé. Droits administrateur requis.");
            return;
        }

        String path = request.getServletPath();

        switch (path) {
            case "/admin/trains/add":
                handleAddTrain(request, response);
                break;
            case "/admin/trains/edit":
                handleEditTrain(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED);
        }
    }

    private void showTrainsList(HttpServletRequest request, HttpServletResponse response, User currentUser)
            throws ServletException, IOException {

        try {
            // Récupérer tous les trains
            List<Train> trains = trainService.findAllTrains();
            System.out.println("🚄 AdminTrainsServlet - Nombre de trains récupérés: " + (trains != null ? trains.size() : "null"));

            // Statistiques
            long totalTrains = trainService.getTotalTrainsCount();
            long activeTrains = trainService.getActiveTrainsCount();
            long inactiveTrains = totalTrains - activeTrains;

            System.out.println("📊 Statistiques trains - Total: " + totalTrains + ", Actifs: " + activeTrains + ", Inactifs: " + inactiveTrains);

            // Préparer les données pour la vue
            request.setAttribute("currentUser", currentUser);
            request.setAttribute("trains", trains);
            request.setAttribute("totalTrains", totalTrains);
            request.setAttribute("activeTrains", activeTrains);
            request.setAttribute("inactiveTrains", inactiveTrains);

            // Messages flash
            String flashType = SessionUtil.getFlashMessageType(request);
            String flashMessage = SessionUtil.getAndRemoveFlashMessage(request);
            if (flashMessage != null) {
                request.setAttribute(flashType != null ? flashType : "info", flashMessage);
            }

        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors du chargement des trains : " + e.getMessage());
        }

        request.getRequestDispatcher("/WEB-INF/views/admin/trains.jsp").forward(request, response);
    }

    private void showAddTrainForm(HttpServletRequest request, HttpServletResponse response, User currentUser)
            throws ServletException, IOException {

        request.setAttribute("currentUser", currentUser);
        request.setAttribute("trainTypes", TrainType.values());

        // Messages flash
        String flashType = SessionUtil.getFlashMessageType(request);
        String flashMessage = SessionUtil.getAndRemoveFlashMessage(request);
        if (flashMessage != null) {
            request.setAttribute(flashType != null ? flashType : "info", flashMessage);
        }

        request.getRequestDispatcher("/WEB-INF/views/admin/add-train.jsp").forward(request, response);
    }

    private void showEditTrainForm(HttpServletRequest request, HttpServletResponse response, User currentUser)
            throws ServletException, IOException {

        String trainIdStr = request.getParameter("id");
        if (trainIdStr == null) {
            SessionUtil.setFlashMessage(request, "error", "ID train manquant");
            response.sendRedirect(request.getContextPath() + "/admin/trains");
            return;
        }

        try {
            Long trainId = Long.parseLong(trainIdStr);
            Train train = trainService.findById(trainId).orElse(null);

            if (train == null) {
                SessionUtil.setFlashMessage(request, "error", "Train non trouvé");
                response.sendRedirect(request.getContextPath() + "/admin/trains");
                return;
            }

            request.setAttribute("currentUser", currentUser);
            request.setAttribute("train", train);
            request.setAttribute("trainTypes", TrainType.values());

            // Messages flash
            String flashType = SessionUtil.getFlashMessageType(request);
            String flashMessage = SessionUtil.getAndRemoveFlashMessage(request);
            if (flashMessage != null) {
                request.setAttribute(flashType != null ? flashType : "info", flashMessage);
            }

            request.getRequestDispatcher("/WEB-INF/views/admin/edit-train.jsp").forward(request, response);

        } catch (NumberFormatException e) {
            SessionUtil.setFlashMessage(request, "error", "ID train invalide");
            response.sendRedirect(request.getContextPath() + "/admin/trains");
        }
    }

    private void handleAddTrain(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Récupérer les paramètres du formulaire
        String trainNumber = request.getParameter("trainNumber");
        String name = request.getParameter("name");
        String capacityStr = request.getParameter("capacity");
        String trainTypeStr = request.getParameter("trainType");
        String imageUrl = request.getParameter("imageUrl");
        String isActiveStr = request.getParameter("isActive");

        try {
            // Validation des paramètres obligatoires
            if (trainNumber == null || name == null || capacityStr == null || trainTypeStr == null) {
                throw new IllegalArgumentException("Tous les champs obligatoires doivent être remplis");
            }

            // Vérifier que le numéro de train n'existe pas déjà
            if (trainService.existsByTrainNumber(trainNumber.trim())) {
                throw new IllegalArgumentException("Ce numéro de train existe déjà");
            }

            Integer capacity = Integer.parseInt(capacityStr);
            TrainType trainType = TrainType.valueOf(trainTypeStr);
            Boolean isActive = "on".equals(isActiveStr) || "true".equals(isActiveStr);

            // Créer le train
            Train newTrain = new Train(trainNumber.trim(), name.trim(), capacity, trainType);
            newTrain.setIsActive(isActive);
            
            // Ajouter l'image si fournie
            if (imageUrl != null && !imageUrl.trim().isEmpty()) {
                newTrain.setImageUrl(imageUrl.trim());
            }

            Train savedTrain = trainService.saveTrain(newTrain);

            SessionUtil.setFlashMessage(request, "success", 
                "Train '" + savedTrain.getName() + "' créé avec succès !");

            response.sendRedirect(request.getContextPath() + "/admin/trains");

        } catch (Exception e) {
            SessionUtil.setFlashMessage(request, "error", 
                "Erreur lors de la création du train : " + e.getMessage());
            response.sendRedirect(request.getContextPath() + "/admin/trains/add");
        }
    }

    private void handleEditTrain(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String trainIdStr = request.getParameter("id");
        String trainNumber = request.getParameter("trainNumber");
        String name = request.getParameter("name");
        String capacityStr = request.getParameter("capacity");
        String trainTypeStr = request.getParameter("trainType");
        String imageUrl = request.getParameter("imageUrl");
        String isActiveStr = request.getParameter("isActive");

        try {
            if (trainIdStr == null) {
                throw new IllegalArgumentException("ID train manquant");
            }

            Long trainId = Long.parseLong(trainIdStr);
            Train train = trainService.findById(trainId).orElse(null);
            
            if (train == null) {
                throw new IllegalArgumentException("Train non trouvé");
            }

            // Vérifier que le numéro de train n'est pas utilisé par un autre train
            if (!train.getTrainNumber().equals(trainNumber.trim())) {
                if (trainService.existsByTrainNumber(trainNumber.trim())) {
                    throw new IllegalArgumentException("Ce numéro de train est déjà utilisé par un autre train");
                }
            }

            Integer capacity = Integer.parseInt(capacityStr);
            TrainType trainType = TrainType.valueOf(trainTypeStr);
            Boolean isActive = "on".equals(isActiveStr) || "true".equals(isActiveStr);

            // Mettre à jour le train
            train.setTrainNumber(trainNumber.trim());
            train.setName(name.trim());
            train.setCapacity(capacity);
            train.setTrainType(trainType);
            train.setIsActive(isActive);
            
            // Mettre à jour l'image
            if (imageUrl != null && !imageUrl.trim().isEmpty()) {
                train.setImageUrl(imageUrl.trim());
            } else {
                train.setImageUrl(null);
            }

            Train updatedTrain = trainService.updateTrain(train);

            SessionUtil.setFlashMessage(request, "success", 
                "Train '" + updatedTrain.getName() + "' mis à jour avec succès !");

            response.sendRedirect(request.getContextPath() + "/admin/trains");

        } catch (Exception e) {
            SessionUtil.setFlashMessage(request, "error", 
                "Erreur lors de la mise à jour du train : " + e.getMessage());
            response.sendRedirect(request.getContextPath() + "/admin/trains/edit?id=" + trainIdStr);
        }
    }

    private void handleDeleteTrain(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String trainIdStr = request.getParameter("id");

        try {
            if (trainIdStr == null) {
                throw new IllegalArgumentException("ID train manquant");
            }

            Long trainId = Long.parseLong(trainIdStr);
            
            // Vérifier que le train existe
            Train train = trainService.findById(trainId).orElse(null);
            if (train == null) {
                throw new IllegalArgumentException("Train non trouvé");
            }

            String trainName = train.getName();
            trainService.deleteTrain(trainId);

            SessionUtil.setFlashMessage(request, "success", 
                "Train '" + trainName + "' supprimé avec succès !");

        } catch (Exception e) {
            SessionUtil.setFlashMessage(request, "error", 
                "Erreur lors de la suppression du train : " + e.getMessage());
        }

        response.sendRedirect(request.getContextPath() + "/admin/trains");
    }
}
