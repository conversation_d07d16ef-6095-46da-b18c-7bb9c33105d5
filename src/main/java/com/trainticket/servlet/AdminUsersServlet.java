package com.trainticket.servlet;

import com.trainticket.model.User;
import com.trainticket.model.UserRole;
import com.trainticket.service.UserService;
import com.trainticket.util.SessionUtil;
import jakarta.servlet.ServletException;
import jakarta.servlet.annotation.WebServlet;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import java.io.IOException;
import java.util.List;

@WebServlet(name = "AdminUsersServlet", urlPatterns = {"/admin/users", "/admin/users/add", "/admin/users/edit", "/admin/users/delete"})
public class AdminUsersServlet extends HttpServlet {

    private UserService userService;

    @Override
    public void init() throws ServletException {
        userService = new UserService();
    }

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Vérifier que l'utilisateur est connecté et est admin
        User currentUser = SessionUtil.getCurrentUser(request);
        if (currentUser == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }

        if (currentUser.getRole() != UserRole.ADMIN) {
            response.sendError(HttpServletResponse.SC_FORBIDDEN, "Accès refusé. Droits administrateur requis.");
            return;
        }

        String path = request.getServletPath();

        switch (path) {
            case "/admin/users":
                showUsersList(request, response, currentUser);
                break;
            case "/admin/users/add":
                showAddUserForm(request, response, currentUser);
                break;
            case "/admin/users/edit":
                showEditUserForm(request, response, currentUser);
                break;
            case "/admin/users/delete":
                handleDeleteUser(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_NOT_FOUND);
        }
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Vérifier que l'utilisateur est connecté et est admin
        User currentUser = SessionUtil.getCurrentUser(request);
        if (currentUser == null) {
            response.sendRedirect(request.getContextPath() + "/login");
            return;
        }

        if (currentUser.getRole() != UserRole.ADMIN) {
            response.sendError(HttpServletResponse.SC_FORBIDDEN, "Accès refusé. Droits administrateur requis.");
            return;
        }

        String path = request.getServletPath();

        switch (path) {
            case "/admin/users/add":
                handleAddUser(request, response);
                break;
            case "/admin/users/edit":
                handleEditUser(request, response);
                break;
            default:
                response.sendError(HttpServletResponse.SC_METHOD_NOT_ALLOWED);
        }
    }

    private void showUsersList(HttpServletRequest request, HttpServletResponse response, User currentUser)
            throws ServletException, IOException {

        try {
            // Récupérer tous les utilisateurs
            List<User> users = userService.getAllUsers();

            // Statistiques
            long totalUsers = userService.getTotalUsersCount();
            long adminCount = userService.getUserCountByRole(UserRole.ADMIN);
            long employeeCount = userService.getUserCountByRole(UserRole.EMPLOYEE);
            long clientCount = userService.getUserCountByRole(UserRole.CLIENT);

            // Préparer les données pour la vue
            request.setAttribute("currentUser", currentUser);
            request.setAttribute("users", users);
            request.setAttribute("totalUsers", totalUsers);
            request.setAttribute("adminCount", adminCount);
            request.setAttribute("employeeCount", employeeCount);
            request.setAttribute("clientCount", clientCount);

            // Messages flash
            String flashMessage = SessionUtil.getAndRemoveFlashMessage(request);
            String flashType = SessionUtil.getFlashMessageType(request);
            if (flashMessage != null) {
                request.setAttribute(flashType != null ? flashType : "info", flashMessage);
            }

        } catch (Exception e) {
            request.setAttribute("error", "Erreur lors du chargement des utilisateurs : " + e.getMessage());
        }

        request.getRequestDispatcher("/WEB-INF/views/admin/users.jsp").forward(request, response);
    }

    private void showAddUserForm(HttpServletRequest request, HttpServletResponse response, User currentUser)
            throws ServletException, IOException {

        request.setAttribute("currentUser", currentUser);
        request.setAttribute("userRoles", UserRole.values());

        // Messages flash
        String flashMessage = SessionUtil.getAndRemoveFlashMessage(request);
        String flashType = SessionUtil.getFlashMessageType(request);
        if (flashMessage != null) {
            request.setAttribute(flashType != null ? flashType : "info", flashMessage);
        }

        request.getRequestDispatcher("/WEB-INF/views/admin/add-user.jsp").forward(request, response);
    }

    private void showEditUserForm(HttpServletRequest request, HttpServletResponse response, User currentUser)
            throws ServletException, IOException {

        String userIdStr = request.getParameter("id");
        if (userIdStr == null) {
            SessionUtil.setFlashMessage(request, "error", "ID utilisateur manquant");
            response.sendRedirect(request.getContextPath() + "/admin/users");
            return;
        }

        try {
            Long userId = Long.parseLong(userIdStr);
            User user = userService.findById(userId).orElse(null);

            if (user == null) {
                SessionUtil.setFlashMessage(request, "error", "Utilisateur non trouvé");
                response.sendRedirect(request.getContextPath() + "/admin/users");
                return;
            }

            request.setAttribute("currentUser", currentUser);
            request.setAttribute("user", user);
            request.setAttribute("userRoles", UserRole.values());

            // Messages flash
            String flashMessage = SessionUtil.getAndRemoveFlashMessage(request);
            String flashType = SessionUtil.getFlashMessageType(request);
            if (flashMessage != null) {
                request.setAttribute(flashType != null ? flashType : "info", flashMessage);
            }

            request.getRequestDispatcher("/WEB-INF/views/admin/edit-user.jsp").forward(request, response);

        } catch (NumberFormatException e) {
            SessionUtil.setFlashMessage(request, "error", "ID utilisateur invalide");
            response.sendRedirect(request.getContextPath() + "/admin/users");
        }
    }

    private void handleAddUser(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        // Récupérer les paramètres du formulaire
        String username = request.getParameter("username");
        String email = request.getParameter("email");
        String password = request.getParameter("password");
        String firstName = request.getParameter("firstName");
        String lastName = request.getParameter("lastName");
        String phoneNumber = request.getParameter("phoneNumber");
        String roleStr = request.getParameter("role");
        String isActiveStr = request.getParameter("isActive");

        try {
            // Validation des paramètres obligatoires
            if (username == null || email == null || password == null || 
                firstName == null || lastName == null || roleStr == null) {
                throw new IllegalArgumentException("Tous les champs obligatoires doivent être remplis");
            }

            UserRole role = UserRole.valueOf(roleStr);
            Boolean isActive = "on".equals(isActiveStr) || "true".equals(isActiveStr);

            // Créer l'utilisateur
            User newUser = userService.createUserWithRole(username, email, password, 
                                                         firstName, lastName, phoneNumber, 
                                                         role, isActive);

            SessionUtil.setFlashMessage(request, "success", 
                "Utilisateur '" + newUser.getUsername() + "' créé avec succès !");

            response.sendRedirect(request.getContextPath() + "/admin/users");

        } catch (Exception e) {
            SessionUtil.setFlashMessage(request, "error", 
                "Erreur lors de la création de l'utilisateur : " + e.getMessage());
            response.sendRedirect(request.getContextPath() + "/admin/users/add");
        }
    }

    private void handleEditUser(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String userIdStr = request.getParameter("id");
        String username = request.getParameter("username");
        String email = request.getParameter("email");
        String firstName = request.getParameter("firstName");
        String lastName = request.getParameter("lastName");
        String phoneNumber = request.getParameter("phoneNumber");
        String roleStr = request.getParameter("role");
        String isActiveStr = request.getParameter("isActive");

        try {
            if (userIdStr == null) {
                throw new IllegalArgumentException("ID utilisateur manquant");
            }

            Long userId = Long.parseLong(userIdStr);
            UserRole role = UserRole.valueOf(roleStr);
            Boolean isActive = "on".equals(isActiveStr) || "true".equals(isActiveStr);

            // Mettre à jour l'utilisateur
            User updatedUser = userService.updateUser(userId, username, email, firstName, 
                                                    lastName, phoneNumber, role, isActive);

            SessionUtil.setFlashMessage(request, "success", 
                "Utilisateur '" + updatedUser.getUsername() + "' mis à jour avec succès !");

            response.sendRedirect(request.getContextPath() + "/admin/users");

        } catch (Exception e) {
            SessionUtil.setFlashMessage(request, "error", 
                "Erreur lors de la mise à jour de l'utilisateur : " + e.getMessage());
            response.sendRedirect(request.getContextPath() + "/admin/users/edit?id=" + userIdStr);
        }
    }

    private void handleDeleteUser(HttpServletRequest request, HttpServletResponse response)
            throws ServletException, IOException {

        String userIdStr = request.getParameter("id");

        try {
            if (userIdStr == null) {
                throw new IllegalArgumentException("ID utilisateur manquant");
            }

            Long userId = Long.parseLong(userIdStr);
            
            // Vérifier que l'utilisateur existe
            User user = userService.findById(userId).orElse(null);
            if (user == null) {
                throw new IllegalArgumentException("Utilisateur non trouvé");
            }

            String username = user.getUsername();
            userService.deleteUser(userId);

            SessionUtil.setFlashMessage(request, "success", 
                "Utilisateur '" + username + "' supprimé avec succès !");

        } catch (Exception e) {
            SessionUtil.setFlashMessage(request, "error", 
                "Erreur lors de la suppression de l'utilisateur : " + e.getMessage());
        }

        response.sendRedirect(request.getContextPath() + "/admin/users");
    }
}
