package com.trainticket.util;

import com.trainticket.model.User;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;

public class SessionUtil {

    private static final String CURRENT_USER_KEY = "currentUser";
    private static final String LOGIN_REDIRECT_URL_KEY = "loginRedirectUrl";

    /**
     * Récupère l'utilisateur connecté depuis la session
     */
    public static User getCurrentUser(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            return (User) session.getAttribute(CURRENT_USER_KEY);
        }
        return null;
    }

    /**
     * Définit l'utilisateur connecté dans la session
     */
    public static void setCurrentUser(HttpServletRequest request, User user) {
        HttpSession session = request.getSession(true);
        session.setAttribute(CURRENT_USER_KEY, user);
    }

    /**
     * Supprime l'utilisateur de la session (déconnexion)
     */
    public static void removeCurrentUser(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            session.removeAttribute(CURRENT_USER_KEY);
        }
    }

    /**
     * Vérifie si un utilisateur est connecté
     */
    public static boolean isUserLoggedIn(HttpServletRequest request) {
        return getCurrentUser(request) != null;
    }

    /**
     * Vérifie si l'utilisateur connecté est un administrateur
     */
    public static boolean isCurrentUserAdmin(HttpServletRequest request) {
        User currentUser = getCurrentUser(request);
        return currentUser != null && currentUser.isAdmin();
    }

    /**
     * Vérifie si l'utilisateur connecté est un employé
     */
    public static boolean isCurrentUserEmployee(HttpServletRequest request) {
        User currentUser = getCurrentUser(request);
        return currentUser != null && currentUser.isEmployee();
    }

    /**
     * Sauvegarde l'URL de redirection après connexion
     */
    public static void setLoginRedirectUrl(HttpServletRequest request, String url) {
        HttpSession session = request.getSession(true);
        session.setAttribute(LOGIN_REDIRECT_URL_KEY, url);
    }

    /**
     * Récupère et supprime l'URL de redirection après connexion
     */
    public static String getAndRemoveLoginRedirectUrl(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            String url = (String) session.getAttribute(LOGIN_REDIRECT_URL_KEY);
            session.removeAttribute(LOGIN_REDIRECT_URL_KEY);
            return url;
        }
        return null;
    }

    /**
     * Invalide complètement la session
     */
    public static void invalidateSession(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            session.invalidate();
        }
    }

    /**
     * Définit un message flash dans la session
     */
    public static void setFlashMessage(HttpServletRequest request, String type, String message) {
        HttpSession session = request.getSession(true);
        session.setAttribute("flashMessageType", type);
        session.setAttribute("flashMessage", message);
    }

    /**
     * Récupère et supprime le message flash de la session
     */
    public static String getAndRemoveFlashMessage(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            String message = (String) session.getAttribute("flashMessage");
            session.removeAttribute("flashMessage");
            session.removeAttribute("flashMessageType");
            return message;
        }
        return null;
    }

    /**
     * Récupère le type du message flash
     */
    public static String getFlashMessageType(HttpServletRequest request) {
        HttpSession session = request.getSession(false);
        if (session != null) {
            return (String) session.getAttribute("flashMessageType");
        }
        return null;
    }

}
