<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modifier le Train - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1e3a8a;
            --secondary-blue: #3b82f6;
            --accent-orange: #f97316;
            --light-blue: #dbeafe;
            --dark-blue: #1e40af;
        }

        body {
            background: linear-gradient(135deg, var(--light-blue) 0%, #f8fafc 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .admin-header {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .form-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            border: none;
        }

        .form-header {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: white;
            padding: 1.5rem;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .info-badge {
            background: linear-gradient(135deg, #f59e0b 0%, #f97316 100%);
            color: white;
            padding: 1rem;
            border-radius: 10px;
            margin-bottom: 1.5rem;
        }

        .btn-admin {
            background: linear-gradient(135deg, var(--accent-orange) 0%, #fb923c 100%);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-admin:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(249, 115, 22, 0.3);
            color: white;
        }

        .btn-secondary {
            background: #6b7280;
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #4b5563;
            color: white;
        }

        .form-control, .form-select {
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-blue);
            box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
        }

        .form-label {
            font-weight: 600;
            color: var(--primary-blue);
            margin-bottom: 0.5rem;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem 1.5rem;
        }

        .form-check-input:checked {
            background-color: var(--secondary-blue);
            border-color: var(--secondary-blue);
        }

        .form-check-label {
            font-weight: 500;
        }

        .required {
            color: #dc2626;
        }

        .image-preview {
            width: 100%;
            max-width: 300px;
            height: 200px;
            border: 2px dashed #e2e8f0;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f8fafc;
            margin-top: 0.5rem;
        }

        .image-preview img {
            max-width: 100%;
            max-height: 100%;
            border-radius: 8px;
        }

        .image-placeholder {
            text-align: center;
            color: #6b7280;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: var(--primary-blue);">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/admin/dashboard">
                <i class="fas fa-train me-2"></i>Train Admin
            </a>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="${pageContext.request.contextPath}/admin/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="${pageContext.request.contextPath}/admin/users">
                            <i class="fas fa-users me-1"></i>Utilisateurs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="${pageContext.request.contextPath}/admin/trains">
                            <i class="fas fa-train me-1"></i>Trains
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="${pageContext.request.contextPath}/admin/routes">
                            <i class="fas fa-route me-1"></i>Trajets
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>${currentUser.firstName} ${currentUser.lastName}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="${pageContext.request.contextPath}/user/profile">Mon Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="${pageContext.request.contextPath}/logout">Déconnexion</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-edit me-3"></i>Modifier le Train
                    </h1>
                    <p class="mb-0 opacity-75">Modifier les informations du train "${train.name}"</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="${pageContext.request.contextPath}/admin/trains" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages -->
    <div class="container">
        <c:if test="${not empty error}">
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>${error}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </c:if>
    </div>

    <!-- Formulaire -->
    <div class="container mb-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="form-card">
                    <div class="form-header">
                        <i class="fas fa-train me-2"></i>Modifier les Informations
                    </div>
                    <div class="p-4">
                        <div class="info-badge">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ID:</strong> ${train.id} | 
                            <strong>Numéro:</strong> ${train.trainNumber}
                        </div>

                        <form method="post" action="${pageContext.request.contextPath}/admin/trains/edit">
                            <input type="hidden" name="id" value="${train.id}">
                            
                            <div class="row g-3">
                                <!-- Numéro de train -->
                                <div class="col-md-6">
                                    <label for="trainNumber" class="form-label">
                                        Numéro de train <span class="required">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="trainNumber" name="trainNumber" 
                                           required maxlength="20" value="${train.trainNumber}">
                                </div>

                                <!-- Nom du train -->
                                <div class="col-md-6">
                                    <label for="name" class="form-label">
                                        Nom du train <span class="required">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="name" name="name" 
                                           required maxlength="100" value="${train.name}">
                                </div>

                                <!-- Type de train -->
                                <div class="col-md-6">
                                    <label for="trainType" class="form-label">
                                        Type de train <span class="required">*</span>
                                    </label>
                                    <select class="form-select" id="trainType" name="trainType" required>
                                        <c:forEach var="type" items="${trainTypes}">
                                            <option value="${type}" ${type == train.trainType ? 'selected' : ''}>
                                                ${type.displayName}
                                            </option>
                                        </c:forEach>
                                    </select>
                                </div>

                                <!-- Capacité -->
                                <div class="col-md-6">
                                    <label for="capacity" class="form-label">
                                        Capacité <span class="required">*</span>
                                    </label>
                                    <input type="number" class="form-control" id="capacity" name="capacity" 
                                           required min="1" max="1000" value="${train.capacity}">
                                </div>

                                <!-- URL de l'image -->
                                <div class="col-md-12">
                                    <label for="imageUrl" class="form-label">URL de l'image</label>
                                    <input type="url" class="form-control" id="imageUrl" name="imageUrl" 
                                           value="${train.imageUrl}" placeholder="Ex: /train-tickets/images/trains/mon-train.jpg">
                                    <div class="form-text">
                                        Laissez vide pour utiliser l'image par défaut. Formats supportés: JPG, PNG, SVG
                                    </div>
                                </div>

                                <!-- Aperçu de l'image -->
                                <div class="col-md-12">
                                    <div class="image-preview" id="imagePreview">
                                        <c:choose>
                                            <c:when test="${train.imageUrl != null && !empty train.imageUrl}">
                                                <img src="${train.imageUrl}" alt="Aperçu" onerror="showImageError()">
                                            </c:when>
                                            <c:otherwise>
                                                <div class="image-placeholder">
                                                    <i class="fas fa-image fa-3x mb-2"></i>
                                                    <p>Aperçu de l'image</p>
                                                </div>
                                            </c:otherwise>
                                        </c:choose>
                                    </div>
                                </div>

                                <!-- Statut actif -->
                                <div class="col-md-12">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="isActive" 
                                               name="isActive" ${train.isActive ? 'checked' : ''}>
                                        <label class="form-check-label" for="isActive">
                                            Train actif
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <hr class="my-4">

                            <div class="d-flex justify-content-between">
                                <a href="${pageContext.request.contextPath}/admin/trains" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>Annuler
                                </a>
                                <button type="submit" class="btn btn-admin">
                                    <i class="fas fa-save me-2"></i>Sauvegarder les Modifications
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Aperçu de l'image
        document.getElementById('imageUrl').addEventListener('input', function() {
            const imageUrl = this.value;
            const preview = document.getElementById('imagePreview');
            
            if (imageUrl) {
                preview.innerHTML = '<img src="' + imageUrl + '" alt="Aperçu" onerror="showImageError()">';
            } else {
                preview.innerHTML = `
                    <div class="image-placeholder">
                        <i class="fas fa-image fa-3x mb-2"></i>
                        <p>Aperçu de l'image</p>
                    </div>
                `;
            }
        });

        function showImageError() {
            document.getElementById('imagePreview').innerHTML = `
                <div class="image-placeholder">
                    <i class="fas fa-exclamation-triangle fa-3x mb-2 text-warning"></i>
                    <p>Impossible de charger l'image</p>
                </div>
            `;
        }
    </script>
</body>
</html>
