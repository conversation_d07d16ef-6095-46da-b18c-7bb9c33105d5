<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="jakarta.tags.core" %>
<%@ taglib prefix="fmt" uri="jakarta.tags.fmt" %>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Modifier un Utilisateur - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1e3a8a;
            --secondary-blue: #3b82f6;
            --accent-orange: #f97316;
            --light-blue: #dbeafe;
            --dark-blue: #1e40af;
        }

        body {
            background: linear-gradient(135deg, var(--light-blue) 0%, #f8fafc 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .admin-header {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .form-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            border: none;
        }

        .form-header {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: white;
            padding: 1.5rem;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .btn-admin {
            background: linear-gradient(135deg, var(--accent-orange) 0%, #fb923c 100%);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-admin:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(249, 115, 22, 0.3);
            color: white;
        }

        .btn-secondary {
            background: #6b7280;
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: #4b5563;
            color: white;
        }

        .form-control, .form-select {
            border: 2px solid #e2e8f0;
            border-radius: 10px;
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--secondary-blue);
            box-shadow: 0 0 0 0.2rem rgba(59, 130, 246, 0.25);
        }

        .form-label {
            font-weight: 600;
            color: var(--primary-blue);
            margin-bottom: 0.5rem;
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem 1.5rem;
        }

        .form-check-input:checked {
            background-color: var(--secondary-blue);
            border-color: var(--secondary-blue);
        }

        .form-check-label {
            font-weight: 500;
        }

        .required {
            color: #dc2626;
        }

        .info-badge {
            background: #e0f2fe;
            color: #0277bd;
            padding: 0.5rem 1rem;
            border-radius: 10px;
            font-size: 0.9rem;
            margin-bottom: 1rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: var(--primary-blue);">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/admin/dashboard">
                <i class="fas fa-train me-2"></i>Train Admin
            </a>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="${pageContext.request.contextPath}/admin/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="${pageContext.request.contextPath}/admin/users">
                            <i class="fas fa-users me-1"></i>Utilisateurs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="${pageContext.request.contextPath}/admin/trains">
                            <i class="fas fa-train me-1"></i>Trains
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="${pageContext.request.contextPath}/admin/routes">
                            <i class="fas fa-route me-1"></i>Trajets
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>${currentUser.firstName} ${currentUser.lastName}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="${pageContext.request.contextPath}/user/profile">Mon Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="${pageContext.request.contextPath}/logout">Déconnexion</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-user-edit me-3"></i>Modifier l'Utilisateur
                    </h1>
                    <p class="mb-0 opacity-75">Modifier les informations de l'utilisateur "${user.username}"</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="${pageContext.request.contextPath}/admin/users" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages -->
    <div class="container">
        <c:if test="${not empty error}">
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>${error}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </c:if>
    </div>

    <!-- Formulaire -->
    <div class="container mb-5">
        <div class="row justify-content-center">
            <div class="col-lg-8">
                <div class="form-card">
                    <div class="form-header">
                        <i class="fas fa-user-edit me-2"></i>Modifier les Informations
                    </div>
                    <div class="p-4">
                        <div class="info-badge">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>ID:</strong> ${user.id} | 
                            <strong>Créé le:</strong> <fmt:formatDate value="${user.createdAt}" pattern="dd/MM/yyyy à HH:mm" />
                        </div>

                        <form method="post" action="${pageContext.request.contextPath}/admin/users/edit">
                            <input type="hidden" name="id" value="${user.id}">
                            
                            <div class="row g-3">
                                <!-- Nom d'utilisateur -->
                                <div class="col-md-6">
                                    <label for="username" class="form-label">
                                        Nom d'utilisateur <span class="required">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="username" name="username" 
                                           required minlength="3" maxlength="50" value="${user.username}"
                                           placeholder="Entrez le nom d'utilisateur">
                                </div>

                                <!-- Email -->
                                <div class="col-md-6">
                                    <label for="email" class="form-label">
                                        Email <span class="required">*</span>
                                    </label>
                                    <input type="email" class="form-control" id="email" name="email" 
                                           required value="${user.email}" placeholder="Entrez l'adresse email">
                                </div>

                                <!-- Rôle -->
                                <div class="col-md-6">
                                    <label for="role" class="form-label">
                                        Rôle <span class="required">*</span>
                                    </label>
                                    <select class="form-select" id="role" name="role" required>
                                        <c:forEach var="userRole" items="${userRoles}">
                                            <option value="${userRole}" ${userRole == user.role ? 'selected' : ''}>
                                                <c:choose>
                                                    <c:when test="${userRole == 'ADMIN'}">Administrateur</c:when>
                                                    <c:when test="${userRole == 'EMPLOYEE'}">Employé</c:when>
                                                    <c:when test="${userRole == 'CLIENT'}">Client</c:when>
                                                    <c:otherwise>${userRole}</c:otherwise>
                                                </c:choose>
                                            </option>
                                        </c:forEach>
                                    </select>
                                </div>

                                <!-- Statut actif -->
                                <div class="col-md-6 d-flex align-items-end">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="isActive" 
                                               name="isActive" ${user.isActive ? 'checked' : ''}>
                                        <label class="form-check-label" for="isActive">
                                            Compte actif
                                        </label>
                                    </div>
                                </div>

                                <!-- Prénom -->
                                <div class="col-md-6">
                                    <label for="firstName" class="form-label">
                                        Prénom <span class="required">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="firstName" name="firstName" 
                                           required maxlength="50" value="${user.firstName}" placeholder="Entrez le prénom">
                                </div>

                                <!-- Nom -->
                                <div class="col-md-6">
                                    <label for="lastName" class="form-label">
                                        Nom <span class="required">*</span>
                                    </label>
                                    <input type="text" class="form-control" id="lastName" name="lastName" 
                                           required maxlength="50" value="${user.lastName}" placeholder="Entrez le nom">
                                </div>

                                <!-- Téléphone -->
                                <div class="col-md-12">
                                    <label for="phoneNumber" class="form-label">Numéro de téléphone</label>
                                    <input type="tel" class="form-control" id="phoneNumber" name="phoneNumber" 
                                           value="${user.phoneNumber}" placeholder="Entrez le numéro de téléphone">
                                </div>
                            </div>

                            <hr class="my-4">

                            <div class="d-flex justify-content-between">
                                <a href="${pageContext.request.contextPath}/admin/users" class="btn btn-secondary">
                                    <i class="fas fa-times me-2"></i>Annuler
                                </a>
                                <button type="submit" class="btn btn-admin">
                                    <i class="fas fa-save me-2"></i>Sauvegarder les Modifications
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
