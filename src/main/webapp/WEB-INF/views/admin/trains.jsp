<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Trains - Admin</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-blue: #1e3a8a;
            --secondary-blue: #3b82f6;
            --accent-orange: #f97316;
            --light-blue: #dbeafe;
            --dark-blue: #1e40af;
        }

        body {
            background: linear-gradient(135deg, var(--light-blue) 0%, #f8fafc 100%);
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            min-height: 100vh;
        }

        .admin-header {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: white;
            padding: 2rem 0;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .stats-card {
            background: white;
            border-radius: 15px;
            padding: 1.5rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: none;
            transition: transform 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
        }

        .stats-icon {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: white;
        }

        .table-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            overflow: hidden;
            border: none;
        }

        .table-header {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: white;
            padding: 1.5rem;
            font-weight: 600;
            font-size: 1.1rem;
        }

        .btn-admin {
            background: linear-gradient(135deg, var(--accent-orange) 0%, #fb923c 100%);
            border: none;
            color: white;
            padding: 0.75rem 1.5rem;
            border-radius: 10px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-admin:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(249, 115, 22, 0.3);
            color: white;
        }

        .action-btn {
            padding: 0.5rem;
            border-radius: 8px;
            border: none;
            margin: 0 0.2rem;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            width: 35px;
            height: 35px;
        }

        .btn-edit {
            background: #10b981;
            color: white;
        }

        .btn-edit:hover {
            background: #059669;
            color: white;
        }

        .btn-delete {
            background: #ef4444;
            color: white;
        }

        .btn-delete:hover {
            background: #dc2626;
            color: white;
        }

        .train-image {
            width: 60px;
            height: 45px;
            object-fit: cover;
            border-radius: 8px;
            border: 2px solid #e2e8f0;
        }

        .no-image {
            width: 60px;
            height: 45px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            border-radius: 8px;
            border: 2px solid #e2e8f0;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            font-size: 0.7rem;
        }

        .status-badge {
            padding: 0.4rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
        }

        .status-active {
            background: #dcfce7;
            color: #166534;
        }

        .status-inactive {
            background: #fee2e2;
            color: #991b1b;
        }

        .train-type-badge {
            padding: 0.3rem 0.6rem;
            border-radius: 15px;
            font-size: 0.75rem;
            font-weight: 600;
            color: white;
        }

        .type-tgv { background: #8b5cf6; }
        .type-intercites { background: #06b6d4; }
        .type-ter { background: #10b981; }
        .type-ouigo { background: #f59e0b; }
        .type-thalys { background: #ef4444; }
        .type-eurostar { background: #6366f1; }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .alert {
            border-radius: 10px;
            border: none;
            padding: 1rem 1.5rem;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark" style="background: var(--primary-blue);">
        <div class="container">
            <a class="navbar-brand" href="${pageContext.request.contextPath}/admin/dashboard">
                <i class="fas fa-train me-2"></i>Train Admin
            </a>

            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="${pageContext.request.contextPath}/admin/dashboard">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="${pageContext.request.contextPath}/admin/users">
                            <i class="fas fa-users me-1"></i>Utilisateurs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="${pageContext.request.contextPath}/admin/trains">
                            <i class="fas fa-train me-1"></i>Trains
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="${pageContext.request.contextPath}/admin/routes">
                            <i class="fas fa-route me-1"></i>Trajets
                        </a>
                    </li>
                </ul>

                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user me-1"></i>${currentUser.firstName} ${currentUser.lastName}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="${pageContext.request.contextPath}/user/profile">Mon Profil</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="${pageContext.request.contextPath}/logout">Déconnexion</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Header -->
    <div class="admin-header">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h1 class="mb-2">
                        <i class="fas fa-train me-3"></i>Gestion des Trains
                    </h1>
                    <p class="mb-0 opacity-75">Gérer tous les trains de votre flotte</p>
                </div>
                <div class="col-md-4 text-end">
                    <a href="${pageContext.request.contextPath}/admin/trains/add" class="btn btn-admin">
                        <i class="fas fa-plus me-2"></i>Ajouter un Train
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Messages -->
    <div class="container">
        <c:if test="${not empty success}">
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>${success}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </c:if>

        <c:if test="${not empty error}">
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle me-2"></i>${error}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        </c:if>
    </div>

    <!-- Statistiques -->
    <div class="container mb-4">
        <div class="row g-4">
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon" style="background: linear-gradient(135deg, var(--secondary-blue), var(--primary-blue));">
                            <i class="fas fa-train"></i>
                        </div>
                        <div class="ms-3">
                            <h3 class="mb-0">${totalTrains}</h3>
                            <p class="text-muted mb-0">Total Trains</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #10b981, #059669);">
                            <i class="fas fa-check-circle"></i>
                        </div>
                        <div class="ms-3">
                            <h3 class="mb-0">${activeTrains}</h3>
                            <p class="text-muted mb-0">Trains Actifs</p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card">
                    <div class="d-flex align-items-center">
                        <div class="stats-icon" style="background: linear-gradient(135deg, #ef4444, #dc2626);">
                            <i class="fas fa-pause-circle"></i>
                        </div>
                        <div class="ms-3">
                            <h3 class="mb-0">${inactiveTrains}</h3>
                            <p class="text-muted mb-0">Trains Inactifs</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Table des trains -->
    <div class="container mb-5">
        <div class="table-card">
            <div class="table-header">
                <i class="fas fa-list me-2"></i>Liste des Trains (${trains.size()})
            </div>
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th>Image</th>
                            <th>Numéro</th>
                            <th>Nom</th>
                            <th>Type</th>
                            <th>Capacité</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <c:forEach var="train" items="${trains}">
                            <tr>
                                <td>
                                    <c:choose>
                                        <c:when test="${train.imageUrl != null && !empty train.imageUrl}">
                                            <img src="${train.imageUrl}" alt="${train.name}" class="train-image">
                                        </c:when>
                                        <c:otherwise>
                                            <div class="no-image">
                                                <i class="fas fa-image"></i>
                                            </div>
                                        </c:otherwise>
                                    </c:choose>
                                </td>
                                <td>
                                    <strong>${train.trainNumber}</strong>
                                </td>
                                <td>${train.name}</td>
                                <td>
                                    <span class="train-type-badge type-${train.trainType.name().toLowerCase()}">
                                        ${train.trainType.displayName}
                                    </span>
                                </td>
                                <td>
                                    <i class="fas fa-users me-1"></i>${train.capacity} places
                                </td>
                                <td>
                                    <c:choose>
                                        <c:when test="${train.isActive}">
                                            <span class="status-badge status-active">
                                                <i class="fas fa-check me-1"></i>Actif
                                            </span>
                                        </c:when>
                                        <c:otherwise>
                                            <span class="status-badge status-inactive">
                                                <i class="fas fa-times me-1"></i>Inactif
                                            </span>
                                        </c:otherwise>
                                    </c:choose>
                                </td>
                                <td>
                                    <a href="${pageContext.request.contextPath}/admin/trains/edit?id=${train.id}" 
                                       class="action-btn btn-edit" title="Modifier">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="#" onclick="confirmDelete(${train.id}, '${train.name}')" 
                                       class="action-btn btn-delete" title="Supprimer">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                        </c:forEach>
                        
                        <c:if test="${empty trains}">
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <i class="fas fa-train fa-3x text-muted mb-3"></i>
                                    <p class="text-muted">Aucun train trouvé</p>
                                    <a href="${pageContext.request.contextPath}/admin/trains/add" class="btn btn-admin">
                                        <i class="fas fa-plus me-2"></i>Ajouter le premier train
                                    </a>
                                </td>
                            </tr>
                        </c:if>
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function confirmDelete(trainId, trainName) {
            if (confirm('Êtes-vous sûr de vouloir supprimer le train "' + trainName + '" ?\n\nCette action est irréversible.')) {
                window.location.href = '${pageContext.request.contextPath}/admin/trains/delete?id=' + trainId;
            }
        }
    </script>
</body>
</html>
