<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Images des Trains</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .train-card {
            border-radius: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        
        .train-card:hover {
            transform: translateY(-5px);
        }
        
        .train-image {
            width: 100%;
            height: 200px;
            object-fit: cover;
            border-radius: 15px 15px 0 0;
        }
        
        .no-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 15px 15px 0 0;
            color: #6b7280;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container mt-5">
        <h1 class="text-center mb-5">Test des Images de Trains</h1>
        
        <div class="row g-4">
            <!-- Test avec image par défaut SVG -->
            <div class="col-md-4">
                <div class="card train-card">
                    <img src="${pageContext.request.contextPath}/images/trains/no-image.svg" 
                         alt="Train par défaut" class="train-image">
                    <div class="card-body">
                        <h5 class="card-title">Train Test SVG</h5>
                        <p class="card-text">Image par défaut SVG</p>
                        <span class="badge bg-primary">TGV</span>
                    </div>
                </div>
            </div>
            
            <!-- Test sans image -->
            <div class="col-md-4">
                <div class="card train-card">
                    <div class="no-image">
                        <span>Aucune image</span>
                    </div>
                    <div class="card-body">
                        <h5 class="card-title">Train sans image</h5>
                        <p class="card-text">Placeholder quand pas d'image</p>
                        <span class="badge bg-secondary">TER</span>
                    </div>
                </div>
            </div>
            
            <!-- Test avec URL d'image externe -->
            <div class="col-md-4">
                <div class="card train-card">
                    <img src="https://via.placeholder.com/400x200/1e3a8a/ffffff?text=Train+Image" 
                         alt="Train externe" class="train-image">
                    <div class="card-body">
                        <h5 class="card-title">Train avec URL externe</h5>
                        <p class="card-text">Image depuis une URL externe</p>
                        <span class="badge bg-warning">OUIGO</span>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="mt-5">
            <h3>Instructions pour ajouter des images :</h3>
            <div class="alert alert-info">
                <h5>1. Exécuter le script SQL :</h5>
                <p>Exécutez le fichier <code>database/add_train_image_column.sql</code> dans votre base de données MySQL via phpMyAdmin ou ligne de commande.</p>
                
                <h5>2. Ajouter des images :</h5>
                <p>Placez vos images de trains dans le dossier <code>src/main/webapp/images/trains/</code></p>
                
                <h5>3. Formats supportés :</h5>
                <ul>
                    <li>JPG, PNG, SVG</li>
                    <li>Taille recommandée : 800x600 pixels</li>
                    <li>Taille maximale : 2MB</li>
                </ul>
                
                <h5>4. Utilisation dans l'application :</h5>
                <p>Les images seront accessibles via : <code>/train-tickets/images/trains/nom-fichier.jpg</code></p>
            </div>
        </div>
        
        <div class="text-center mt-4">
            <a href="${pageContext.request.contextPath}/" class="btn btn-primary">
                Retour à l'accueil
            </a>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
